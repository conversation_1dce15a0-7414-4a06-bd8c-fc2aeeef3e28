/**
 * Tests for ChartCard component
 */

import React from 'react'
import { render, screen, fireEvent } from '../../../../test-utils'
import ChartCard from '../ChartCard'
import { mockTask, mockTasks } from '../../../../__mocks__/mockData'

// Mock recharts components to avoid canvas issues in tests
jest.mock('recharts', () => ({
	Bar: () => <div data-testid="bar-chart" />,
	BarChart: ({ children }: { children: React.ReactNode }) => (
		<div data-testid="bar-chart-container">{children}</div>
	),
	CartesianGrid: () => <div data-testid="cartesian-grid" />,
	ResponsiveContainer: ({ children }: { children: React.ReactNode }) => (
		<div data-testid="responsive-container">{children}</div>
	),
	Tooltip: () => <div data-testid="tooltip" />,
	XAxis: () => <div data-testid="x-axis" />,
	YAxis: () => <div data-testid="y-axis" />,
}))

describe('ChartCard Component', () => {
	describe('when no task is selected', () => {
		it('should display placeholder message', () => {
			// Arrange & Act
			render(<ChartCard selectedTask={null} />)

			// Assert
			expect(screen.getByText('Select a task to view details')).toBeInTheDocument()
		})

		it('should not display chart or table', () => {
			// Arrange & Act
			render(<ChartCard selectedTask={null} />)

			// Assert
			expect(screen.queryByTestId('bar-chart-container')).not.toBeInTheDocument()
			expect(screen.queryByRole('table')).not.toBeInTheDocument()
		})
	})

	describe('when a task is selected', () => {
		it('should display task name and progress', () => {
			// Arrange & Act
			render(<ChartCard selectedTask={mockTask} />)

			// Assert
			expect(screen.getByText(/Task Alpha/)).toBeInTheDocument()
			expect(screen.getByText(/85%/)).toBeInTheDocument()
		})

		it('should display time data table', () => {
			// Arrange & Act
			render(<ChartCard selectedTask={mockTask} />)

			// Assert
			expect(screen.getByRole('table')).toBeInTheDocument()
			expect(screen.getByText('Planned')).toBeInTheDocument()
			expect(screen.getByText('Actual')).toBeInTheDocument()
		})

		it('should display planned time data correctly', () => {
			// Arrange & Act
			render(<ChartCard selectedTask={mockTask} />)

			// Assert
			expect(screen.getByText('08:00')).toBeInTheDocument()
			expect(screen.getByText('17:00')).toBeInTheDocument()
			expect(screen.getByText('9h')).toBeInTheDocument()
		})

		it('should display actual time data correctly', () => {
			// Arrange & Act
			render(<ChartCard selectedTask={mockTask} />)

			// Assert
			expect(screen.getByText('08:15')).toBeInTheDocument()
			expect(screen.getByText('17:30')).toBeInTheDocument()
			expect(screen.getByText('9h 15m')).toBeInTheDocument()
		})

		it('should display chart components', () => {
			// Arrange & Act
			render(<ChartCard selectedTask={mockTask} />)

			// Assert
			expect(screen.getByTestId('responsive-container')).toBeInTheDocument()
			expect(screen.getByTestId('bar-chart-container')).toBeInTheDocument()
			expect(screen.getByTestId('bar-chart')).toBeInTheDocument()
		})

		it('should display table headers correctly', () => {
			// Arrange & Act
			render(<ChartCard selectedTask={mockTask} />)

			// Assert
			expect(screen.getByText('Type')).toBeInTheDocument()
			expect(screen.getByText('Start')).toBeInTheDocument()
			expect(screen.getByText('End')).toBeInTheDocument()
			expect(screen.getByText('Duration')).toBeInTheDocument()
		})
	})

	describe('with different task data', () => {
		it('should handle task with 100% progress', () => {
			// Arrange
			const completedTask = mockTasks[1] // Task Beta with 100% progress

			// Act
			render(<ChartCard selectedTask={completedTask} />)

			// Assert
			expect(screen.getByText(/Task Beta/)).toBeInTheDocument()
			expect(screen.getByText(/100%/)).toBeInTheDocument()
		})

		it('should handle task with 0% progress', () => {
			// Arrange
			const notStartedTask = mockTasks[2] // Task Gamma with 0% progress

			// Act
			render(<ChartCard selectedTask={notStartedTask} />)

			// Assert
			expect(screen.getByText(/Task Gamma/)).toBeInTheDocument()
			expect(screen.getByText(/0%/)).toBeInTheDocument()
		})

		it('should handle task with empty actual times', () => {
			// Arrange
			const notStartedTask = mockTasks[2] // Task Gamma with empty actual times

			// Act
			render(<ChartCard selectedTask={notStartedTask} />)

			// Assert
			// const table = screen.getByRole('table')
			const rows = screen.getAllByRole('row')
			const actualRow = rows[2] // The "Actual" row
			const cells = actualRow.querySelectorAll('td')

			// Check that actual start, end, and duration cells are empty
			expect(cells).toHaveLength(3) // Should have 3 cells
			expect(cells[0]).toHaveTextContent('') // Empty actual start
			expect(cells[1]).toHaveTextContent('') // Empty actual end
			expect(cells[2]).toHaveTextContent('') // Empty actual duration
		})
	})

	describe('component memoization', () => {
		it('should not re-render when props are equal', () => {
			// Arrange
			const { rerender } = render(<ChartCard selectedTask={mockTask} />)
			const initialRender = screen.getByText(/Task Alpha/)

			// Act
			rerender(<ChartCard selectedTask={mockTask} />)

			// Assert
			expect(screen.getByText(/Task Alpha/)).toBe(initialRender)
		})

		it('should re-render when task changes', () => {
			// Arrange
			const { rerender } = render(<ChartCard selectedTask={mockTask} />)

			// Act
			rerender(<ChartCard selectedTask={mockTasks[1]} />)

			// Assert
			expect(screen.getByText(/Task Beta/)).toBeInTheDocument()
			expect(screen.queryByText(/Task Alpha/)).not.toBeInTheDocument()
		})
	})

	describe('fullscreen functionality', () => {
		it('should display fullscreen button when onFullView is provided', () => {
			// Arrange
			const mockOnFullView = jest.fn()
			render(<ChartCard selectedTask={mockTask} onFullView={mockOnFullView} />)

			// Assert
			expect(screen.getByLabelText('fullscreen chart')).toBeInTheDocument()
			expect(screen.getByRole('button', { name: /fullscreen/i })).toBeInTheDocument()
		})

		it('should not display fullscreen button when onFullView is not provided', () => {
			// Arrange
			render(<ChartCard selectedTask={mockTask} />)

			// Assert
			expect(screen.queryByLabelText('fullscreen chart')).not.toBeInTheDocument()
		})

		it('should not display fullscreen button when no task is selected', () => {
			// Arrange
			const mockOnFullView = jest.fn()
			render(<ChartCard selectedTask={null} onFullView={mockOnFullView} />)

			// Assert
			expect(screen.queryByLabelText('fullscreen chart')).not.toBeInTheDocument()
		})

		it('should call onFullView when fullscreen button is clicked', () => {
			// Arrange
			const mockOnFullView = jest.fn()
			render(<ChartCard selectedTask={mockTask} onFullView={mockOnFullView} />)

			// Act
			const fullscreenButton = screen.getByLabelText('fullscreen chart')
			fireEvent.click(fullscreenButton)

			// Assert
			expect(mockOnFullView).toHaveBeenCalledTimes(1)
			expect(mockOnFullView).toHaveBeenCalledWith(
				expect.any(Object),
				`${mockTask.name} - Detailed View`,
			)
		})

		it('should have proper tooltip for fullscreen button', () => {
			// Arrange
			const mockOnFullView = jest.fn()
			render(<ChartCard selectedTask={mockTask} onFullView={mockOnFullView} />)

			// Assert
			const fullscreenButton = screen.getByLabelText('fullscreen chart')
			// The tooltip is handled by MUI Tooltip component, so we check for the button's presence
			expect(fullscreenButton).toBeInTheDocument()
		})
	})

	describe('accessibility', () => {
		it('should have proper table structure', () => {
			// Arrange & Act
			render(<ChartCard selectedTask={mockTask} />)

			// Assert
			expect(screen.getByRole('table')).toBeInTheDocument()
			expect(screen.getAllByRole('row')).toHaveLength(3) // Header + 2 data rows
			expect(screen.getAllByRole('columnheader')).toHaveLength(4)
		})

		it('should have proper heading hierarchy', () => {
			// Arrange & Act
			render(<ChartCard selectedTask={mockTask} />)

			// Assert
			const heading = screen.getByRole('heading', { level: 6 })
			expect(heading).toBeInTheDocument()
			expect(heading).toHaveTextContent(/Task Alpha/)
		})
	})
})
