import type { PlannedWorkImportResponse } from '../../../models/PlannedWork'
import { httpPost } from '../../../core/api/httpClient'
import { endpoints } from '../../../core/api/endpoints'

/**
 * Upload Excel file for planned work import
 * @param file - Excel file to upload
 * @param signal - AbortSignal for request cancellation
 * @returns Promise with import response
 */
export async function importPlannedWork(
	file: File,
	signal?: AbortSignal,
): Promise<PlannedWorkImportResponse> {
	// Validate file type
	const allowedTypes = [
		'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', // .xlsx
		'application/vnd.ms-excel', // .xls
	]

	if (!allowedTypes.includes(file.type)) {
		throw new Error('Invalid file type. Please upload an Excel file (.xlsx or .xls)')
	}

	// Validate file size (max 10MB)
	const maxSize = 10 * 1024 * 1024 // 10MB
	if (file.size > maxSize) {
		throw new Error('File size too large. Maximum allowed size is 10MB')
	}

	// Create FormData for file upload
	const formData = new FormData()
	formData.append('file', file)

	return httpPost<PlannedWorkImportResponse>(endpoints.plannedWorkImport, formData, {
		signal,
	})
}
