import {
	Table,
	TableBody,
	TableCell,
	TableHead,
	TableRow,
	Box,
} from '@mui/material'
import type { Task } from '../../../models/Task'
import { useTranslation } from 'react-i18next'
import { memo } from 'react'
import { getAlertColors } from '../../../common/utils/alertColors'

function rowsChecksum(rows: Task[]): string {
	return JSON.stringify(
		rows.map((r) => ({
			id: r.id,
			rowName: r.rowName,
			shippingDate: r.shippingDate,
			vangp: r.vangp,
			deliveryTime: r.deliveryTime,
			plannedStart: r.plannedStart,
			plannedEnd: r.plannedEnd,
			plannedDuration: r.plannedDuration,
			actualStart: r.actualStart,
			actualEnd: r.actualEnd,
			actualDuration: r.actualDuration,
		})),
	)
}

interface FullscreenDataTableProps {
	rows: Task[]
	selectedRowId?: number | null
	onSelect?: (row: Task) => void
}

function FullscreenDataTableComponent({
	rows,
	selectedRowId,
	onSelect,
}: FullscreenDataTableProps) {
	const { t } = useTranslation()

	// Constants for sticky header positioning
	const mainHeaderHeight = 48
	const subHeaderHeight = 48
	const mainHeaderTop = 0
	const subHeaderTop = mainHeaderHeight
	const mainHeaderZIndex = 12
	const subHeaderZIndex = 11

	return (
		<Box sx={{ height: 'calc(90dvh - 120px)', overflow: 'auto' }}>
			<Table stickyHeader size="small">
				<TableHead>
					{/* Top-level headers */}
					<TableRow sx={{ height: mainHeaderHeight }}>
						<TableCell
							colSpan={4}
							align="center"
							sx={{
								position: 'sticky',
								top: mainHeaderTop,
								zIndex: mainHeaderZIndex,
								backgroundColor: 'background.paper',
								borderBottom: '1px solid',
								borderColor: 'divider',
								fontWeight: 600,
							}}
						>
							{t('table.name')}
						</TableCell>
						<TableCell
							colSpan={3}
							align="center"
							sx={{
								position: 'sticky',
								top: mainHeaderTop,
								zIndex: mainHeaderZIndex,
								backgroundColor: 'background.paper',
								borderBottom: '1px solid',
								borderColor: 'divider',
								fontWeight: 600,
							}}
						>
							{t('table.planned')}
						</TableCell>
						<TableCell
							colSpan={3}
							align="center"
							sx={{
								position: 'sticky',
								top: mainHeaderTop,
								zIndex: mainHeaderZIndex,
								backgroundColor: 'background.paper',
								borderBottom: '1px solid',
								borderColor: 'divider',
								fontWeight: 600,
							}}
						>
							{t('table.actual')}
						</TableCell>
						<TableCell
							colSpan={2}
							align="center"
							sx={{
								position: 'sticky',
								top: mainHeaderTop,
								zIndex: mainHeaderZIndex,
								backgroundColor: 'background.paper',
								borderBottom: '1px solid',
								borderColor: 'divider',
								fontWeight: 600,
							}}
						>
							{t('table.duration')}
						</TableCell>
					</TableRow>

					{/* Sub-level headers */}
					<TableRow sx={{ height: subHeaderHeight }}>
						<TableCell
							align="center"
							sx={{
								position: 'sticky',
								top: subHeaderTop,
								zIndex: subHeaderZIndex,
								backgroundColor: 'background.paper',
								fontWeight: 600,
								minWidth: 80,
							}}
						>
							{t('table.row')}
						</TableCell>
						<TableCell
							align="center"
							sx={{
								position: 'sticky',
								top: subHeaderTop,
								zIndex: subHeaderZIndex,
								backgroundColor: 'background.paper',
								fontWeight: 600,
								minWidth: 120,
							}}
						>
							{t('table.shippingDate')}
						</TableCell>
						<TableCell
							align="center"
							sx={{
								position: 'sticky',
								top: subHeaderTop,
								zIndex: subHeaderZIndex,
								backgroundColor: 'background.paper',
								fontWeight: 600,
								minWidth: 80,
							}}
						>
							{t('table.vangp')}
						</TableCell>
						<TableCell
							align="center"
							sx={{
								position: 'sticky',
								top: subHeaderTop,
								zIndex: subHeaderZIndex,
								backgroundColor: 'background.paper',
								fontWeight: 600,
								minWidth: 120,
							}}
						>
							{t('table.deliveryTime')}
						</TableCell>
						<TableCell
							align="center"
							sx={{
								position: 'sticky',
								top: subHeaderTop,
								zIndex: subHeaderZIndex,
								backgroundColor: 'background.paper',
								fontWeight: 600,
								minWidth: 100,
							}}
						>
							{t('table.start')}
						</TableCell>
						<TableCell
							align="center"
							sx={{
								position: 'sticky',
								top: subHeaderTop,
								zIndex: subHeaderZIndex,
								backgroundColor: 'background.paper',
								fontWeight: 600,
								minWidth: 100,
							}}
						>
							{t('table.end')}
						</TableCell>
						<TableCell
							align="center"
							sx={{
								position: 'sticky',
								top: subHeaderTop,
								zIndex: subHeaderZIndex,
								backgroundColor: 'background.paper',
								fontWeight: 600,
								minWidth: 100,
							}}
						>
							{t('table.duration')}
						</TableCell>
						<TableCell
							align="center"
							sx={{
								position: 'sticky',
								top: subHeaderTop,
								zIndex: subHeaderZIndex,
								backgroundColor: 'background.paper',
								fontWeight: 600,
								minWidth: 100,
							}}
						>
							{t('table.start')}
						</TableCell>
						<TableCell
							align="center"
							sx={{
								position: 'sticky',
								top: subHeaderTop,
								zIndex: subHeaderZIndex,
								backgroundColor: 'background.paper',
								fontWeight: 600,
								minWidth: 100,
							}}
						>
							{t('table.end')}
						</TableCell>
						<TableCell
							align="center"
							sx={{
								position: 'sticky',
								top: subHeaderTop,
								zIndex: subHeaderZIndex,
								backgroundColor: 'background.paper',
								fontWeight: 600,
								minWidth: 100,
							}}
						>
							{t('table.duration')}
						</TableCell>
						<TableCell
							align="center"
							sx={{
								position: 'sticky',
								top: subHeaderTop,
								zIndex: subHeaderZIndex,
								backgroundColor: 'background.paper',
								fontWeight: 600,
								minWidth: 100,
							}}
						>
							{t('table.planned')}
						</TableCell>
						<TableCell
							align="center"
							sx={{
								position: 'sticky',
								top: subHeaderTop,
								zIndex: subHeaderZIndex,
								backgroundColor: 'background.paper',
								fontWeight: 600,
								minWidth: 100,
							}}
						>
							{t('table.actual')}
						</TableCell>
					</TableRow>
				</TableHead>
				<TableBody>
					{rows.map((row, index) => {
						const colors = getAlertColors(row.alerts)
						const isSelected = row.id === selectedRowId
						return (
							<TableRow
								key={`${row.id}-${index}`}
								hover
								selected={isSelected}
								onClick={() => onSelect?.(row)}
								sx={{
									cursor: onSelect ? 'pointer' : 'default',
									backgroundColor: `${colors.row} !important`,
									'& .MuiTableCell-root': {
										backgroundColor: 'transparent',
									},
									'&:hover': {
										backgroundColor: `${colors.row} !important`,
										filter: 'brightness(0.95)',
									},
									'&.Mui-selected': {
										backgroundColor: `${colors.header} !important`,
										'& .MuiTableCell-root': {
											color: '#fff',
										},
									},
								}}
							>
								<TableCell align="center">{row.rowName}</TableCell>
								<TableCell align="center">{row.shippingDate}</TableCell>
								<TableCell align="center">{row.vangp}</TableCell>
								<TableCell align="center">{row.deliveryTime}</TableCell>
								<TableCell align="center">{row.plannedStart}</TableCell>
								<TableCell align="center">{row.plannedEnd}</TableCell>
								<TableCell align="center">{row.plannedDuration}</TableCell>
								<TableCell align="center">{row.actualStart}</TableCell>
								<TableCell align="center">{row.actualEnd}</TableCell>
								<TableCell align="center">{row.actualDuration}</TableCell>
								<TableCell align="center">{row.plannedDuration}</TableCell>
								<TableCell align="center">{row.actualDuration}</TableCell>
							</TableRow>
						)
					})}
				</TableBody>
			</Table>
		</Box>
	)
}

const FullscreenDataTable = memo(FullscreenDataTableComponent, (prev, next) => {
	return (
		rowsChecksum(prev.rows) === rowsChecksum(next.rows) &&
		prev.selectedRowId === next.selectedRowId
	)
})

export default FullscreenDataTable
