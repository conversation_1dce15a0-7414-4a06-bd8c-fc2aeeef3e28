// Environment configuration
interface Config {
	apiBaseUrl: string
	debugHttp: boolean
	dataRefreshRate: number
}

// Environment variable access (can be mocked in tests)
const getEnv = () => {
	if (typeof import.meta === 'undefined' || !import.meta.env) {
		// In test environment, check for mocked import.meta first
		const globalWithImport = globalThis as unknown as {
			import?: { meta?: { env?: Record<string, string | boolean> } }
		}

		if (typeof globalThis !== 'undefined' && globalWithImport.import?.meta?.env) {
			console.log('Using mocked import.meta.env for testing')
			return globalWithImport.import.meta.env
		}

		// In browser/Vite environment, try to access import.meta.env safely
		try {
			if (typeof window !== 'undefined' && typeof globalThis !== 'undefined') {
				// Use eval to avoid TypeScript compilation issues with import.meta
				const importMetaEnv = eval(
					'typeof import !== "undefined" && import.meta && import.meta.env',
				)
				if (importMetaEnv) {
					console.log(
						'Using import.meta.env from browser:',
						importMetaEnv.MODE || 'development',
					)
					return importMetaEnv
				}
			}
		} catch (error) {
			console.log('Failed to access import.meta.env:', error)
		}
	}
	console.log('Using import.meta.env from Vite build:', import.meta.env)
	return import.meta.env
}

// Runtime configuration loaded from public/config.json in production
let runtimeConfig: Config | null = null

// Load configuration from public/config.json
async function loadRuntimeConfig(): Promise<Config | null> {
	if (runtimeConfig) {
		return runtimeConfig
	}

	try {
		const env = getEnv()
		console.log('loadRuntimeConfig: env.MODE =', env.MODE)

		// Only load config.json in production mode
		if (env.MODE === 'production') {
			const response = await fetch('/config.json')
			if (response.ok) {
				runtimeConfig = await response.json()
				console.log('✓ Runtime configuration loaded from config.json')
				return runtimeConfig
			}
		}
	} catch (error) {
		console.warn('Failed to load runtime configuration:', error)
	}

	return null
}

// Get API base URL - uses runtime config in production, environment variables in development
export async function getApiBaseUrl(): Promise<string> {
	const env = getEnv()

	// In production mode, ONLY use config.json (no .env files)
	if (env.MODE === 'production') {
		const config = await loadRuntimeConfig()
		if (config?.apiBaseUrl) {
			return config.apiBaseUrl
		}
		// In production, if config.json fails, return empty string (should not happen)
		console.warn('Production mode: config.json not available, API calls may fail')
		return ''
	}

	// In development modes (dev, mock, test), use environment variables
	if (env.MODE === 'dev' || env.MODE === 'mock' || env.MODE === 'test') {
		return String(env.VITE_API_BASE_URL || '')
	}

	// Fallback (should not reach here in normal cases)
	return String(env.VITE_API_BASE_URL || '')
}

// Get debug HTTP setting
export async function getDebugHttp(): Promise<boolean> {
	const env = getEnv()

	// In production mode, ONLY use config.json (no .env files)
	if (env.MODE === 'production') {
		const config = await loadRuntimeConfig()
		if (config && typeof config.debugHttp === 'boolean') {
			return config.debugHttp
		}
		// In production, default to false if config.json fails
		return false
	}

	// In development modes (dev, mock, test), use environment variables
	if (env.MODE === 'dev' || env.MODE === 'mock' || env.MODE === 'test') {
		return String(env.VITE_DEBUG_HTTP) === 'true'
	}

	// Fallback (should not reach here in normal cases)
	return String(env.VITE_DEBUG_HTTP) === 'true'
}

// Get data refresh rate setting
export async function getDataRefreshRate(): Promise<number> {
	const env = getEnv()

	// In development, use environment variables
	if (env.MODE === 'dev' || env.MODE === 'mock' || env.MODE === 'test') {
		const refreshRate = parseInt(String(env.VITE_DATA_REFRESH_RATE || '10000'), 10)
		return isNaN(refreshRate) ? 10000 : refreshRate
	}

	// In production, try to load from config.json first
	const config = await loadRuntimeConfig()
	if (config && typeof config.dataRefreshRate === 'number') {
		return config.dataRefreshRate
	}

	// Fallback to environment variable if config.json fails
	const fallbackRate = parseInt(String(env.VITE_DATA_REFRESH_RATE || '7000'), 10)
	return isNaN(fallbackRate) ? 7000 : fallbackRate
}

// Legacy ENV object for backward compatibility (deprecated - use async functions above)
export const ENV = {
	// These will be empty in production - use getApiBaseUrl() and getDebugHttp() instead
	API_BASE_URL: String(getEnv().VITE_API_BASE_URL || ''),
	DEBUG_HTTP: String(getEnv().VITE_DEBUG_HTTP) === 'true' || false,
	DATA_REFRESH_RATE: parseInt(String(getEnv().VITE_DATA_REFRESH_RATE || '10000'), 10) || 10000,
}
