import { useEffect, useRef, useState } from 'react'
import type { Task } from '../../../models/Task'
import type { Operator } from '../../../models/Operator'
import { fetchTasksForOperator } from '../services/tasks'
import { getDataRefreshRate } from '../../../core/config/env'
import FullscreenDataTable from './FullscreenDataTable'
import FullscreenChartCard from './FullscreenChartCard'

function isAbortError(err: unknown): boolean {
	if (err instanceof DOMException) return err.name === 'AbortError'
	if (typeof err === 'object' && err !== null && 'name' in err) {
		const name = (err as { name?: unknown }).name
		return typeof name === 'string' && name === 'AbortError'
	}
	return false
}

function computeChecksum(data: Task[]): string {
	return JSON.stringify(
		data.map((task) => ({
			id: task.id,
			name: task.name,
			rowName: task.rowName,
			shippingDate: task.shippingDate,
			vangp: task.vangp,
			deliveryTime: task.deliveryTime,
			plannedStart: task.plannedStart,
			plannedEnd: task.plannedEnd,
			plannedDuration: task.plannedDuration,
			actualStart: task.actualStart,
			actualEnd: task.actualEnd,
			actualDuration: task.actualDuration,
			progressRate: task.progressRate,
			alerts: task.alerts,
		})),
	)
}

interface FullscreenViewProps {
	operator: Operator
	initialTasks: Task[]
	initialSelectedTask: Task | null
	viewType: 'table' | 'chart'
	onTaskSelect?: (task: Task) => void
}

export default function FullscreenView({
	operator,
	initialTasks,
	initialSelectedTask,
	viewType,
	onTaskSelect,
}: FullscreenViewProps) {
	const [tasks, setTasks] = useState<Task[]>(initialTasks)
	const [selectedTask, setSelectedTask] = useState<Task | null>(initialSelectedTask)
	const prevChecksumRef = useRef<string>(computeChecksum(initialTasks))

	// Update selected task when tasks change
	useEffect(() => {
		if (tasks.length > 0 && selectedTask) {
			const updatedTask = tasks.find((t) => t.id === selectedTask.id)
			if (updatedTask && updatedTask !== selectedTask) {
				setSelectedTask(updatedTask)
			} else if (!updatedTask) {
				// Selected task is no longer in the array, clear selection
				setSelectedTask(null)
			}
		} else if (tasks.length === 0) {
			// No tasks available, clear selection
			setSelectedTask(null)
		}
	}, [tasks, selectedTask])

	// Set up data refresh for fullscreen mode
	useEffect(() => {
		let isCancelled = false
		let intervalId: ReturnType<typeof setInterval> | undefined = undefined
		let currentController: AbortController | null = null

		const fetchAndUpdateTasks = async () => {
			if (isCancelled) return

			// Abort previous request
			if (currentController) {
				currentController.abort()
			}

			currentController = new AbortController()

			try {
				const data = await fetchTasksForOperator(operator.name, currentController.signal)

				if (isCancelled || currentController.signal.aborted) return

				const checksum = computeChecksum(data)
				if (checksum !== prevChecksumRef.current) {
					prevChecksumRef.current = checksum
					setTasks(data)
				}
			} catch (error) {
				if (!isAbortError(error) && !isCancelled) {
					console.error('Failed to fetch tasks in fullscreen:', error)
				}
			}
		}

		const initializeRefresh = async () => {
			try {
				const refreshRate = await getDataRefreshRate()
				if (isCancelled) return

				// Initial fetch
				await fetchAndUpdateTasks()

				if (isCancelled) return

				// Set up interval for continuous refresh
				intervalId = setInterval(fetchAndUpdateTasks, refreshRate)
			} catch (error) {
				console.error('Failed to initialize fullscreen refresh:', error)
			}
		}

		initializeRefresh()

		return () => {
			isCancelled = true
			if (currentController) {
				currentController.abort()
			}
			clearInterval(intervalId)
		}
	}, [operator.name])

	const handleTaskSelect = (task: Task) => {
		setSelectedTask(task)
		onTaskSelect?.(task)
	}

	if (viewType === 'table') {
		return (
			<FullscreenDataTable
				rows={tasks}
				selectedRowId={selectedTask?.id ?? null}
				onSelect={handleTaskSelect}
			/>
		)
	}

	if (viewType === 'chart' && selectedTask) {
		return <FullscreenChartCard selectedTask={selectedTask} />
	}

	// Fallback for chart view with no selected task
	return (
		<div className="flex items-center justify-center h-full">
			<p className="text-gray-500">No task selected for chart view</p>
		</div>
	)
}
