{"name": "vmapp", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite --mode dev", "dev:mock": "vite --mode mock", "build": "tsc -b --noEmit && vite build", "lint": "eslint .", "preview": "vite preview", "typecheck": "tsc -b --noEmit", "format": "prettier --write .", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "changeset": "changeset", "changeset:version": "changeset version", "changeset:publish": "changeset publish"}, "dependencies": {"@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.1", "@mui/icons-material": "^7.3.1", "@mui/material": "^7.3.1", "i18next": "^25.4.2", "react": "^19.1.1", "react-dom": "^19.1.1", "react-i18next": "^15.7.2", "react-intersection-observer": "^9.16.0", "react-router-dom": "^7.8.2", "recharts": "^3.1.2", "zustand": "^5.0.8"}, "devDependencies": {"@changesets/cli": "^2.29.6", "@eslint/js": "^9.33.0", "@tailwindcss/postcss": "^4.1.12", "@testing-library/jest-dom": "^6.8.0", "@testing-library/react": "^16.3.0", "@types/jest": "^30.0.0", "@types/node": "^24.5.1", "@types/react": "^19.1.10", "@types/react-dom": "^19.1.7", "@vitejs/plugin-react": "^5.0.0", "autoprefixer": "^10.4.21", "cross-env": "^10.0.0", "eslint": "^9.33.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^16.3.0", "jest": "^30.1.2", "jest-environment-jsdom": "^30.1.2", "msw": "^2.10.5", "postcss": "^8.5.6", "prettier": "^3.6.2", "tailwindcss": "^4.1.12", "ts-jest": "^29.4.1", "typescript": "~5.8.3", "typescript-eslint": "^8.39.1", "vite": "^7.1.2"}, "msw": {"workerDirectory": ["public"]}}