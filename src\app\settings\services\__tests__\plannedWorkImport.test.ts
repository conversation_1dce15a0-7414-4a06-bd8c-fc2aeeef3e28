/**
 * Tests for planned work import service
 */

import { importPlannedWork } from '../plannedWorkImport'
import { httpPost } from '../../../../core/api/httpClient'
import { endpoints } from '../../../../core/api/endpoints'

// Mock the httpClient module
jest.mock('../../../../core/api/httpClient', () => ({
	httpPost: jest.fn(),
}))

const mockHttpPost = httpPost as jest.MockedFunction<typeof httpPost>

describe('Planned Work Import Service', () => {
	beforeEach(() => {
		jest.clearAllMocks()
	})

	describe('importPlannedWork', () => {
		it('should successfully import Excel file', async () => {
			// Arrange
			const mockFile = new File(['test content'], 'test.xlsx', {
				type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
			})
			const mockResponse = {
				totalRecords: 27,
				successfulInserts: 20,
				successfulUpdates: 5,
				failedRecords: 2,
				errors: ['2 rows were skipped due to invalid data'],
				warnings: [],
				message: 'Import completed successfully',
			}

			mockHttpPost.mockResolvedValue(mockResponse)

			// Act
			const result = await importPlannedWork(mockFile)

			// Assert
			expect(result).toEqual(mockResponse)
			expect(mockHttpPost).toHaveBeenCalledWith(
				endpoints.plannedWorkImport,
				expect.any(FormData),
				{ signal: undefined },
			)

			// Verify FormData contains the file
			const formDataCall = mockHttpPost.mock.calls[0][1] as FormData
			expect(formDataCall.get('file')).toBe(mockFile)
		})

		it('should handle request with abort signal', async () => {
			// Arrange
			const mockFile = new File(['test content'], 'test.xlsx', {
				type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
			})
			const mockResponse = {
				totalRecords: 10,
				successfulInserts: 8,
				successfulUpdates: 2,
				failedRecords: 0,
				errors: [],
				warnings: [],
				message: 'Import completed successfully',
			}
			const abortController = new AbortController()
			const signal = abortController.signal

			mockHttpPost.mockResolvedValue(mockResponse)

			// Act
			const result = await importPlannedWork(mockFile, signal)

			// Assert
			expect(result).toEqual(mockResponse)
			expect(mockHttpPost).toHaveBeenCalledWith(
				endpoints.plannedWorkImport,
				expect.any(FormData),
				{ signal },
			)
		})

		it('should reject invalid file type', async () => {
			// Arrange
			const mockFile = new File(['test content'], 'test.txt', {
				type: 'text/plain',
			})

			// Act & Assert
			await expect(importPlannedWork(mockFile)).rejects.toThrow(
				'Invalid file type. Please upload an Excel file (.xlsx or .xls)',
			)
			expect(mockHttpPost).not.toHaveBeenCalled()
		})

		it('should reject file that is too large', async () => {
			// Arrange
			const largeContent = 'x'.repeat(11 * 1024 * 1024) // 11MB
			const mockFile = new File([largeContent], 'large.xlsx', {
				type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
			})

			// Act & Assert
			await expect(importPlannedWork(mockFile)).rejects.toThrow(
				'File size too large. Maximum allowed size is 10MB',
			)
			expect(mockHttpPost).not.toHaveBeenCalled()
		})

		it('should accept .xls file type', async () => {
			// Arrange
			const mockFile = new File(['test content'], 'test.xls', {
				type: 'application/vnd.ms-excel',
			})
			const mockResponse = {
				totalRecords: 15,
				successfulInserts: 15,
				successfulUpdates: 0,
				failedRecords: 0,
				errors: [],
				warnings: [],
				message: 'Import completed successfully',
			}

			mockHttpPost.mockResolvedValue(mockResponse)

			// Act
			const result = await importPlannedWork(mockFile)

			// Assert
			expect(result).toEqual(mockResponse)
			expect(mockHttpPost).toHaveBeenCalledWith(
				endpoints.plannedWorkImport,
				expect.any(FormData),
				{ signal: undefined },
			)
		})

		it('should handle API errors', async () => {
			// Arrange
			const mockFile = new File(['test content'], 'test.xlsx', {
				type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
			})
			const apiError = new Error('HTTP 500')

			mockHttpPost.mockRejectedValue(apiError)

			// Act & Assert
			await expect(importPlannedWork(mockFile)).rejects.toThrow('HTTP 500')
			expect(mockHttpPost).toHaveBeenCalledWith(
				endpoints.plannedWorkImport,
				expect.any(FormData),
				{ signal: undefined },
			)
		})
	})
})
