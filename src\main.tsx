import { StrictMode } from 'react'
import { createRoot } from 'react-dom/client'
import './index.css'
import App from './App'

async function enableMocking() {
	// Only enable MSW if explicitly requested via environment variable
	// This allows the Vite proxy to work when VITE_USE_MSW is not set
	if (import.meta.env.MODE === 'mock' && import.meta.env.VITE_USE_MSW === 'true') {
		const { worker } = await import('./mocks/browser')
		await worker.start({ serviceWorker: { url: '/mockServiceWorker.js' } })
		console.log('🔧 MSW enabled for API mocking')
	} else if (import.meta.env.MODE === 'dev') {
		console.log('🔧 MSW disabled - using Vite proxy for API calls')
	}
}

enableMocking().finally(() => {
	createRoot(document.getElementById('root')!).render(
		<StrictMode>
			<App />
		</StrictMode>,
	)
})
