import { createContext, useContext, type PropsWithChildren } from 'react'

interface AuthContextValue {
	isAuthed: boolean
}

const AuthContext = createContext<AuthContextValue>({ isAuthed: true })

// eslint-disable-next-line react-refresh/only-export-components
export function useAuth() {
	return useContext(AuthContext)
}

export default function AuthProvider({ children }: PropsWithChildren) {
	return <AuthContext.Provider value={{ isAuthed: true }}>{children}</AuthContext.Provider>
}
