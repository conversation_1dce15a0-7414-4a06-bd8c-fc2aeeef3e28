{"extends": "./tsconfig.app.json", "compilerOptions": {"module": "ESNext", "moduleResolution": "bundler", "target": "ES2022", "lib": ["ES2022", "DOM", "DOM.Iterable"], "types": ["jest", "@testing-library/jest-dom", "node"], "jsx": "react-jsx", "esModuleInterop": true, "allowSyntheticDefaultImports": true, "skipLibCheck": true, "strict": true, "noEmit": false, "verbatimModuleSyntax": false, "allowImportingTsExtensions": false}, "include": ["src/**/*", "src/**/*.test.ts", "src/**/*.test.tsx", "src/**/*.spec.ts", "src/**/*.spec.tsx"], "exclude": ["node_modules", "dist"]}