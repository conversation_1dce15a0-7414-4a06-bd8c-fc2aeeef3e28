import { create } from 'zustand'
import { persist, createJSONStorage } from 'zustand/middleware'

interface AppState {
	locale: 'en' | 'ja'
	setLocale: (l: 'en' | 'ja') => void
	themeMode: 'light' | 'dark'
	setThemeMode: (m: 'light' | 'dark') => void
	toggleThemeMode: () => void
	fontScale: number
	setFontScale: (s: number) => void
}

// Safe localStorage wrapper with error handling
const createSafeStorage = () => {
	const isStorageAvailable = () => {
		try {
			const test = '__storage_test__'
			localStorage.setItem(test, test)
			localStorage.removeItem(test)
			return true
		} catch {
			return false
		}
	}

	if (!isStorageAvailable()) {
		console.warn('localStorage is not available, falling back to memory storage')
		// Fallback to memory storage
		const memoryStorage = new Map<string, string>()
		return {
			getItem: (name: string) => memoryStorage.get(name) ?? null,
			setItem: (name: string, value: string) => {
				memoryStorage.set(name, value)
			},
			removeItem: (name: string) => {
				memoryStorage.delete(name)
			},
		}
	}

	return {
		getItem: (name: string) => {
			try {
				return localStorage.getItem(name)
			} catch (error) {
				console.error('Error reading from localStorage:', error)
				return null
			}
		},
		setItem: (name: string, value: string) => {
			try {
				localStorage.setItem(name, value)
			} catch (error) {
				console.error('Error writing to localStorage:', error)
			}
		},
		removeItem: (name: string) => {
			try {
				localStorage.removeItem(name)
			} catch (error) {
				console.error('Error removing from localStorage:', error)
			}
		},
	}
}

export const useAppStore = create<AppState>()(
	persist(
		(set, get) => ({
			locale: 'en',
			setLocale: (locale) => set({ locale }),
			themeMode: 'light',
			setThemeMode: (themeMode) => set({ themeMode }),
			toggleThemeMode: () =>
				set({ themeMode: get().themeMode === 'light' ? 'dark' : 'light' }),
			fontScale: 1,
			setFontScale: (fontScale) => set({ fontScale }),
		}),
		{
			name: 'vam-app-settings', // unique name for localStorage key
			storage: createJSONStorage(() => createSafeStorage()),
			// Only persist the settings we want to save
			partialize: (state) => ({
				locale: state.locale,
				themeMode: state.themeMode,
				fontScale: state.fontScale,
			}),
			// Handle migration for future schema changes
			version: 1,
			migrate: (persistedState: unknown, version: number) => {
				if (version === 0) {
					// Migration logic for future versions
					return persistedState
				}
				return persistedState
			},
			// Skip hydration during SSR
			skipHydration: false,
		},
	),
)
