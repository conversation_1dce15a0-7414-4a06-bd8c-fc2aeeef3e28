import {
	Paper,
	Typography,
	useTheme,
	Table,
	TableBody,
	TableCell,
	TableContainer,
	TableHead,
	TableRow,
	Box,
	IconButton,
	Tooltip,
	Card,
	CardContent,
} from '@mui/material'
import FullscreenIcon from '@mui/icons-material/Fullscreen'
import {
	Bar,
	<PERSON><PERSON>hart,
	CartesianGrid,
	ResponsiveContainer,
	Tooltip as RechartsTooltip,
	XAxis,
	YAxis,
} from 'recharts'
import type { Task } from '../../../models/Task'
import { useTranslation } from 'react-i18next'
import { memo } from 'react'
import { getAlertColors } from '../../../common/utils/alertColors'

interface ChartCardProps {
	selectedTask: Task | null
	onFullView?: (content: React.ReactNode, title?: string) => void
}

function areTasksEqual(a: Task | null, b: Task | null): boolean {
	if (a === b) return true
	if (!a || !b) return a === b
	return (
		a.id === b.id &&
		a.rowName === b.rowName &&
		a.name === b.name &&
		a.progressRate === b.progressRate &&
		a.plannedStart === b.plannedStart &&
		a.plannedEnd === b.plannedEnd &&
		a.plannedDuration === b.plannedDuration &&
		a.actualStart === b.actualStart &&
		a.actualEnd === b.actualEnd &&
		a.actualDuration === b.actualDuration
	)
}

function ChartCardComponent({ selectedTask, onFullView }: ChartCardProps) {
	const theme = useTheme()
	const { t } = useTranslation()

	if (!selectedTask) {
		return (
			<Paper className="p-3 sm:p-4">
				<Typography variant="subtitle1" color="text.secondary">
					Select a task to view details
				</Typography>
			</Paper>
		)
	}

	const data = [{ name: selectedTask.name, value: selectedTask.progressRate }]

	const timeData = [
		{
			type: 'Planned',
			start: selectedTask.plannedStart,
			end: selectedTask.plannedEnd,
			duration: selectedTask.plannedDuration,
		},
		{
			type: 'Actual',
			start: selectedTask.actualStart,
			end: selectedTask.actualEnd,
			duration: selectedTask.actualDuration,
		},
	]

	return (
		<Paper
			className="p-3 sm:p-4 space-y-3 sm:space-y-4 grid grid-rows-auto h-full"
			sx={{
				'& .MuiTableHead-root': {
					backgroundColor: getAlertColors(selectedTask.alerts).header,
				},
				'& .MuiTableRow-root': {
					backgroundColor: getAlertColors(selectedTask.alerts).row,
				},
			}}
		>
			{/* Header with title and fullscreen */}
			{selectedTask ? (
				<Box
					className="flex items-center justify-between"
					sx={{
						backgroundColor: getAlertColors(selectedTask.alerts).header,
						padding: 1,
						borderRadius: 1,
					}}
				>
					<Typography variant="subtitle1" fontWeight={600} sx={{ color: '#fff' }}>
						{selectedTask.name} - {t('chart.progress')}: {selectedTask.progressRate}%
					</Typography>
					{onFullView ? (
						<Tooltip title={t('common.fullscreen') ?? 'Fullscreen'}>
							<IconButton
								size="small"
								aria-label="fullscreen chart"
								onClick={() => {
									const alertColors = getAlertColors(selectedTask.alerts)
									const fullScreenContent = (
										<Box
											className="w-full h-full gap-1.5"
											sx={{
												height: 'calc(100dvh - 180px)',
												backgroundColor: theme.palette.background.default,
												display: 'flex',
												flexDirection: { xs: 'column', md: 'row' },
											}}
										>
											{/* Task Information Section */}
											<Box sx={{ flex: { xs: '1', md: '0 0 33%' } }}>
												<Card
													elevation={3}
													sx={{
														height: '100%',
													}}
												>
													<CardContent
														sx={{
															height: '100%',
															display: 'flex',
															flexDirection: 'column',
														}}
													>
														<Typography
															variant="h5"
															fontWeight={700}
															gutterBottom
														>
															{selectedTask.name}
														</Typography>
														<Typography
															variant="h3"
															fontWeight={600}
															sx={{
																mb: 2,
																color: getAlertColors(
																	selectedTask.alerts,
																).progressRate,
															}}
														>
															{t('chart.progress')}:{' '}
															{selectedTask.progressRate}%
														</Typography>

														<Box sx={{ flexGrow: 1 }}>
															<Typography
																variant="h6"
																fontWeight={600}
																gutterBottom
															>
																Time Analysis
															</Typography>
															<TableContainer>
																<Table
																	sx={{ backgroundColor: '#fff' }}
																>
																	<TableHead>
																		<TableRow>
																			<TableCell
																				align="center"
																				sx={{
																					fontWeight: 600,
																					backgroundColor:
																						alertColors.background,
																				}}
																			>
																				{t('table.type')}
																			</TableCell>
																			<TableCell
																				align="center"
																				sx={{
																					fontWeight: 600,
																					backgroundColor:
																						alertColors.background,
																				}}
																			>
																				{t('table.start')}
																			</TableCell>
																			<TableCell
																				align="center"
																				sx={{
																					fontWeight: 600,
																					backgroundColor:
																						alertColors.background,
																				}}
																			>
																				{t('table.end')}
																			</TableCell>
																			<TableCell
																				align="center"
																				sx={{
																					fontWeight: 600,
																					backgroundColor:
																						alertColors.background,
																				}}
																			>
																				{t(
																					'table.duration',
																				)}
																			</TableCell>
																		</TableRow>
																	</TableHead>
																	<TableBody>
																		{timeData.map(
																			(row, index) => (
																				<TableRow
																					key={row.type}
																					sx={{
																						'&:last-child td, &:last-child th':
																							{
																								border: 0,
																							},
																						backgroundColor:
																							index %
																								2 ===
																							0
																								? alertColors.row
																								: 'transparent',
																					}}
																				>
																					<TableCell
																						align="center"
																						component="th"
																						scope="row"
																						sx={{
																							fontWeight: 600,
																						}}
																					>
																						{row.type}
																					</TableCell>
																					<TableCell align="center">
																						{row.start}
																					</TableCell>
																					<TableCell align="center">
																						{row.end}
																					</TableCell>
																					<TableCell align="center">
																						{
																							row.duration
																						}
																					</TableCell>
																				</TableRow>
																			),
																		)}
																	</TableBody>
																</Table>
															</TableContainer>
														</Box>
													</CardContent>
												</Card>
											</Box>

											{/* Chart Section */}
											<Box sx={{ flex: { xs: '1', md: '0 0 67%' } }}>
												<Card elevation={3} sx={{ height: '100%' }}>
													<CardContent
														sx={{
															height: '100%',
															display: 'flex',
															flexDirection: 'column',
														}}
													>
														<Typography
															variant="h6"
															fontWeight={600}
															gutterBottom
														>
															Progress Chart
														</Typography>
														<Box sx={{ flexGrow: 1, minHeight: 400 }}>
															<ResponsiveContainer
																width="100%"
																height="100%"
															>
																<BarChart
																	data={data}
																	margin={{
																		top: 32,
																		right: 32,
																		left: 32,
																		bottom: 32,
																	}}
																>
																	<CartesianGrid
																		stroke={
																			theme.palette.divider
																		}
																		strokeDasharray="3 3"
																	/>
																	<XAxis
																		dataKey="name"
																		stroke={
																			theme.palette.text
																				.secondary
																		}
																		fontSize={16}
																		tick={{
																			fill: theme.palette.text
																				.secondary,
																		}}
																	/>
																	<YAxis
																		domain={[0, 100]}
																		tickFormatter={(value) =>
																			`${value}%`
																		}
																		stroke={
																			theme.palette.text
																				.secondary
																		}
																		fontSize={16}
																		tick={{
																			fill: theme.palette.text
																				.secondary,
																		}}
																	/>
																	<RechartsTooltip
																		formatter={(value) => [
																			`${value}%`,
																			'Progress',
																		]}
																		contentStyle={{
																			backgroundColor:
																				theme.palette
																					.background
																					.paper,
																			border: `1px solid ${theme.palette.divider}`,
																			borderRadius: 8,
																			color: theme.palette
																				.text.primary,
																			fontSize: 14,
																		}}
																	/>
																	<Bar
																		dataKey="value"
																		fill={
																			alertColors.background
																		}
																		radius={[8, 8, 0, 0]}
																		stroke={alertColors.header}
																		strokeWidth={2}
																	/>
																</BarChart>
															</ResponsiveContainer>
														</Box>
													</CardContent>
												</Card>
											</Box>
										</Box>
									)

									onFullView(
										fullScreenContent,
										`${selectedTask.name} - Detailed View`,
									)
								}}
							>
								<FullscreenIcon fontSize="small" />
							</IconButton>
						</Tooltip>
					) : null}
				</Box>
			) : null}
			{/* Time Data Table */}
			<Box>
				<TableContainer>
					<Table size="small">
						<TableHead>
							<TableRow>
								<TableCell>{t('table.type')}</TableCell>
								<TableCell align="center">{t('table.start')}</TableCell>
								<TableCell align="center">{t('table.end')}</TableCell>
								<TableCell align="center">{t('table.duration')}</TableCell>
							</TableRow>
						</TableHead>
						<TableBody>
							{timeData.map((row) => (
								<TableRow
									key={row.type}
									sx={{ '&:last-child td, &:last-child th': { border: 0 } }}
								>
									<TableCell component="th" scope="row" sx={{ fontWeight: 500 }}>
										{row.type}
									</TableCell>
									<TableCell align="center">{row.start}</TableCell>
									<TableCell align="center">{row.end}</TableCell>
									<TableCell align="center">{row.duration}</TableCell>
								</TableRow>
							))}
						</TableBody>
					</Table>
				</TableContainer>
			</Box>

			{/* Chart Section */}
			<Box sx={{ display: 'flex', justifyContent: 'center', width: '100%' }}>
				<Box
					sx={{
						width: '100%',
						maxWidth: { xs: 280, sm: 320 },
						height: { xs: 180, sm: 200 },
					}}
				>
					<ResponsiveContainer width="100%" height="100%">
						<BarChart data={data} margin={{ top: 16, right: 16, left: 16, bottom: 16 }}>
							<CartesianGrid stroke={theme.palette.divider} strokeDasharray="3 3" />
							<XAxis
								dataKey="name"
								stroke={theme.palette.text.secondary}
								fontSize={12}
								tick={{ fill: theme.palette.text.secondary }}
							/>
							<YAxis
								domain={[0, 100]}
								tickFormatter={(value) => `${value}%`}
								stroke={theme.palette.text.secondary}
								fontSize={12}
								tick={{ fill: theme.palette.text.secondary }}
							/>
							<RechartsTooltip
								formatter={(value) => [`${value}%`, 'Progress']}
								contentStyle={{
									backgroundColor: theme.palette.background.paper,
									border: `1px solid ${theme.palette.divider}`,
									borderRadius: 8,
									color: theme.palette.text.primary,
								}}
							/>
							<Bar
								dataKey="value"
								fill={getAlertColors(selectedTask.alerts).background}
								radius={[4, 4, 0, 0]}
								stroke={getAlertColors(selectedTask.alerts).header}
								strokeWidth={1}
							/>
						</BarChart>
					</ResponsiveContainer>
				</Box>
			</Box>
		</Paper>
	)
}

const ChartCard = memo(ChartCardComponent, (prev, next) =>
	areTasksEqual(prev.selectedTask, next.selectedTask),
)

export default ChartCard
